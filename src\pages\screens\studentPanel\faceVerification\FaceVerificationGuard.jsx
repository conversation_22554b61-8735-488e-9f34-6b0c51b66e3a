
import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import FaceVerification from './FaceVerification';

const FaceVerificationGuard = ({ children }) => {
  const [showVerification, setShowVerification] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  // Get userId from sessionStorage
  const userId = sessionStorage.getItem('userId');

  // If no userId, redirect immediately to /sasthra
  if (!userId) {
    return <Navigate to="/sasthra" replace />;
  }

  // Check verification status
  useEffect(() => {
    const verified = sessionStorage.getItem(`verified_${userId}`);
    if (verified) {
      setIsVerified(true);
    } else {
      setShowVerification(true);
    }
  }, [userId]);

  // Clear verification status on unmount
  useEffect(() => {
    return () => {
      if (userId) {
        sessionStorage.removeItem(`verified_${userId}`);
      }
    };
  }, [userId]);

  // Handle verification success
  const handleVerificationSuccess = ({ success }) => {
    if (success) {
      setIsVerified(true);
      sessionStorage.setItem(`verified_${userId}`, 'true');
      setShowVerification(false);
    }
  };

  // If verified, render the protected component
  if (isVerified) {
    return children;
  }

  // Show verification modal
  return (
    <FaceVerification
      showVerification={showVerification}
      setShowVerification={setShowVerification}
      userId={userId}
      onVerificationSuccess={handleVerificationSuccess}
    />
  );
};

export default FaceVerificationGuard;
