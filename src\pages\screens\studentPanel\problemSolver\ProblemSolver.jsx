import { useState, useEffect, useRef } from 'react';
import {
  useDoubtSolverServiceMutation,
  useGenerateAudioServiceMutation,
  useYoutubeSearchServiceMutation
} from './problemSolver.Slice';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router';
import { MathJaxContext, MathJax } from 'better-react-mathjax';
import YouTubeVideos from './YouTubeVidoes';
import robotImg from '../../../../assets/Voice_assistance_gif.gif';
import {
  FaHistory,
  FaTrash,
  FaPaperPlane,
  FaMicrophone,
  FaImage,
  FaStop,
  FaArrowLeft,
  FaUpload,
  FaVideo,
  FaPlay,
  FaTimes,
  FaExpand,
  FaPlus,
  FaPhone,
  FaVolumeUp,
  FaChalkboardTeacher
} from 'react-icons/fa';
import { GiFairyWand } from 'react-icons/gi';
import ReactCrop from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import ReferTutorModal from './ReferTutorModal';
import Toastify from '../../../../components/PopUp/Toastify';

const ProblemSolver = () => {
  const navigate = useNavigate();
  const [userId, setUserId] = useState('');
  const [text, setText] = useState('');
  const [audio, setAudio] = useState(null);
  const [image, setImage] = useState(null);
  const [mode, setMode] = useState('tutor');
  const [language, setLanguage] = useState('English');
  const [response, setResponse] = useState([]);
  const [audioUrl, setAudioUrl] = useState('');
  const [youtubeResults, setYoutubeResults] = useState([]);
  const [previousYoutubeResults, setPreviousYoutubeResults] = useState([]);
  const [error, setError] = useState('');
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [activeInput, setActiveInput] = useState(null);
  const [particles, setParticles] = useState([]);
  const [morphState, setMorphState] = useState('idle');
  const [energyLevel, setEnergyLevel] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [responseHistory, setResponseHistory] = useState([]);
  const [reset, setReset] = useState(false);
  const [includeHistory, setIncludeHistory] = useState(true);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [showYouTube, setShowYouTube] = useState(false);
  const [isYouTubeMaximized, setIsYouTubeMaximized] = useState(false);
  const [showMediaOptions, setShowMediaOptions] = useState(false);
  const [showReferTutorModal, setShowReferTutorModal] = useState(false);
  const [showCropModal, setShowCropModal] = useState(false);
  const [imageSrc, setImageSrc] = useState(null);
  const [crop, setCrop] = useState({ aspect: 1, unit: '%', width: 50, height: 50 });
  const [croppedImage, setCroppedImage] = useState(null);
  const [mathJaxError, setMathJaxError] = useState(false);
  const [aiThinking, setAiThinking] = useState(false);
  const [showWebcamModal, setShowWebcamModal] = useState(false);
  const [videoStream, setVideoStream] = useState(null);

  const [res, setRes] = useState(null);

  const chatContainerRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioInputRef = useRef(null);
  const imageInputRef = useRef(null);
  const audioRef = useRef(null);
  const imageRef = useRef(null);
  const videoRef = useRef(null);

  const languageMap = {
    English: 'english',
    'Hindi+English': 'hinglish',
    'Telugu+English': 'tenglish',
    'Malayalam+English': 'manglish',
    'Kannada+English': 'kanglish',
    'Tamil+English': 'tanglish'
  };

  const [doubtSolver, { isLoading: isDoubtSolverLoading, error: doubtSolverError }] =
    useDoubtSolverServiceMutation();
  const [youtubeSearch, { isLoading: isYoutubeLoading, error: youtubeError }] =
    useYoutubeSearchServiceMutation();
  const [generateAudio, { isLoading: isAudioLoading, error: audioError }] =
    useGenerateAudioServiceMutation();

  const mathJaxConfig = {
    loader: { load: ['input/tex', 'output/svg'] },
    tex: {
      inlineMath: [
        ['$', '$'],
        ['\\(', '\\)']
      ],
      displayMath: [
        ['$$', '$$'],
        ['\\[', '\\]']
      ],
      svg: { fontCache: 'global' }
    }
  };

  const startWebcam = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      setVideoStream(stream);
      setShowWebcamModal(true);
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play().catch((err) => console.error('Play error:', err));
        } else {
          setRes({
            status: 400,
            message: 'Video element not found. Please try again.'
          });
        }
      }, 100);
      setHasUserInteracted(true);
      setRes({
        status: 200,
        data: { message: 'Webcam started successfully!' }
      });
    } catch (err) {
      console.error('Webcam error:', err);
      setRes({
        status: 400,
        message: `Failed to access webcam: ${err.message}. Please allow camera access.`
      });
    }
  };

  const stopWebcam = () => {
    if (videoStream) {
      videoStream.getTracks().forEach((track) => track.stop());
      setVideoStream(null);
      setShowWebcamModal(false);
    }
  };

  const handleCaptureImage = () => {
    if (videoRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setImageSrc(imageDataUrl);
      setShowCropModal(true);
      stopWebcam();
      setRes({
        status: 200,
        data: { message: 'Image captured successfully!' }
      });
    }
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current.scrollTo({
          top: chatContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }, 100);
    }
  };

  useEffect(() => {
    let storedUserId = sessionStorage.getItem('user_id');
    if (!storedUserId) {
      storedUserId = 'student_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('user_id', storedUserId);
    }
    setUserId(storedUserId);
  }, []);

  useEffect(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 3 + 1,
      speed: Math.random() * 2 + 0.5,
      color: `rgba(${Math.random() > 0.5 ? '59, 130, 246' : '139, 92, 246'}, ${Math.random() * 0.4 + 0.1})`
    }));
    setParticles(newParticles);
    const interval = setInterval(() => {
      setParticles((prev) =>
        prev.map((particle) => ({
          ...particle,
          x: (particle.x + particle.speed) % window.innerWidth,
          y: particle.y + Math.sin(Date.now() * 0.002 + particle.id) * 1
        }))
      );
    }, 50);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (isDoubtSolverLoading || isYoutubeLoading || isAudioLoading) {
      setAiThinking(true);
      const interval = setInterval(() => {
        setEnergyLevel((prev) => (prev + 10) % 100);
      }, 150);
      return () => clearInterval(interval);
    } else {
      setAiThinking(false);
      setEnergyLevel(0);
    }
  }, [isDoubtSolverLoading, isYoutubeLoading, isAudioLoading]);

  useEffect(() => {
    let timer;
    if (isRecording) {
      timer = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [isRecording]);

  useEffect(() => {
    if (audioUrl && hasUserInteracted) {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      audioRef.current = new Audio(audioUrl);
      audioRef.current
        .play()
        .then(() => {
          console.log('Audio playback started');
          setIsAudioPlaying(true);
        })
        .catch((err) => {
          console.error('Audio playback failed:', err);
        });
      audioRef.current.onended = () => {
        setIsAudioPlaying(false);
      };
    }
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
        setIsAudioPlaying(false);
      }
    };
  }, [audioUrl, hasUserInteracted]);

  useEffect(() => {
    scrollToBottom();
  }, [response, aiThinking]);

  useEffect(() => {
    if (aiThinking) {
      scrollToBottom();
    }
  }, [aiThinking]);

  const handleDeleteAudio = () => {
    setAudio(null);
    if (activeInput === 'audio') {
      setActiveInput(null);
    }
  };

  const handleDeleteImage = () => {
    setImage(null);
    if (activeInput === 'image') {
      setActiveInput(null);
    }
  };

  const handleRecord = async () => {
    setHasUserInteracted(true);
    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream, { mimeType: 'audio/webm' });
        const chunks = [];
        mediaRecorderRef.current.ondataavailable = (e) => {
          if (e.data.size > 0) {
            chunks.push(e.data);
          }
        };
        mediaRecorderRef.current.onstop = () => {
          const blob = new Blob(chunks, { type: 'audio/webm' });
          const audioFile = new File([blob], `recorded_audio_${Date.now()}.webm`, {
            type: 'audio/webm'
          });
          setAudio(audioFile);
          stream.getTracks().forEach((track) => track.stop());
          mediaRecorderRef.current = null;
          setRes({
            status: 200,
            data: { message: 'Audio recorded successfully!' }
          });
        };
        mediaRecorderRef.current.start();
        setIsRecording(true);
        setActiveInput('audio');
        setMorphState('expanding');
        setEnergyLevel(50);
        setRes({
          status: 200,
          data: { message: 'Recording started.' }
        });
      } catch (err) {
        setRes({
          status: 400,
          message: 'Failed to access microphone. Please allow microphone access.'
        });
      }
    } else {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        setRecordingTime(0);
        setMorphState('idle');
        setEnergyLevel(0);
      }
    }
  };

  const handleAudioUpload = (e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file && !isRecording) {
      setAudio(file);
      setActiveInput('audio');
      setMorphState('upload');
      setEnergyLevel(50);
      setRes({
        status: 200,
        data: { message: 'Audio uploaded successfully!' }
      });
      setTimeout(() => setMorphState('idle'), 800);
    } else if (isRecording) {
      setRes({
        status: 400,
        message: 'Cannot upload audio while recording is in progress.'
      });
    }
  };

  const handleImageUpload = (e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImageSrc(reader.result);
        setShowCropModal(true);
        setRes({
          status: 200,
          data: { message: 'Image selected for cropping.' }
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerateAudio = async (responseText) => {
    setHasUserInteracted(true);
    if (!userId) {
      setRes({
        status: 400,
        message: 'User ID not found. Please refresh the page or log in again.'
      });
      return;
    }
    setMorphState('processing');
    setEnergyLevel(100);
    const formData = new FormData();
    formData.append('response', responseText);
    formData.append('language', languageMap[language] || 'english');
    formData.append('user_id', userId);

    try {
      const audioResult = await generateAudio(formData).unwrap();
      setAudioUrl(`data:audio/mp3;base64,${audioResult.audio_base64}`);
      setRes({
        status: 200,
        data: { message: 'Audio generated successfully!' }
      });
      setMorphState('celebrating');
      setTimeout(() => {
        setMorphState('idle');
        setEnergyLevel(0);
      }, 3000);
    } catch (err) {
      console.error('Audio generation error:', err);
      setRes({
        status: err.status || 500,
        response: { data: { message: err.data?.detail || 'Failed to generate audio.' } }
      });
      setMorphState('idle');
      setEnergyLevel(0);
    }
  };

  const handleSubmit = async () => {
    setText('');
    setHasUserInteracted(true);
    if (audioRef.current && isAudioPlaying) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsAudioPlaying(false);
    }
    setAudioUrl('');
    if (!userId) {
      setRes({
        status: 400,
        message: 'User ID not found. Please refresh the page or log in again.'
      });
      return;
    }
    if (!text && !audio && !image) {
      setRes({
        status: 400,
        message: 'Please provide a text, audio, or image input.'
      });
      return;
    }
    setMorphState('processing');
    setEnergyLevel(100);
    const formData = new FormData();
    if (text) formData.append('text', text);
    if (audio) formData.append('audio', audio);
    if (image) formData.append('image', image);
    const currentMode = mode || 'tutor';
    const currentLanguage = languageMap[language] || 'english';
    formData.append('mode', currentMode);
    formData.append('language', currentLanguage);
    formData.append('user_id', userId);
    formData.append('reset', reset.toString());
    formData.append('include_history', 'true');
    setError('');
    setAudio(null);
    setImage(null);
    setFeedbackMessage('');
    setActiveInput(null);
    setMathJaxError(false);

    const doubtSolverPromise = doubtSolver(formData)
      .unwrap()
      .then((doubtResult) => {
        const responseContent = doubtResult.response?.response || doubtResult.response || '';
        setResponse((prev) => [
          ...prev,
          { type: 'user', content: text || (audio ? 'Audio Query' : 'Image Query') },
          { type: 'bot', content: responseContent }
        ]);
        setRes({
          status: 200,
          data: { message: 'Question answered successfully!' }
        });
        if (doubtResult.history) {
          const serverHistory = [];
          for (let i = 0; i < doubtResult.history.length; i++) {
            if (doubtResult.history[i].role === 'user') {
              const nextMessage = doubtResult.history[i + 1];
              serverHistory.push({
                question: doubtResult.history[i].content || 'Query',
                response:
                  nextMessage && nextMessage.role === 'assistant' ? nextMessage.content || '' : ''
              });
            }
          }
          setResponseHistory(serverHistory);
        }
      })
      .catch((err) => {
        console.error('Doubt solver error:', err);
        setRes({
          status: err.status || 500,
          response: { data: { message: err.data?.detail || 'Failed to process your query.' } }
        });
      });

    const youtubeSearchPromise = youtubeSearch(formData)
      .unwrap()
      .then((youtubeResult) => {
        const newVideos = youtubeResult.videos || [];
        setYoutubeResults(newVideos);
        if (newVideos.length > 0) {
          setPreviousYoutubeResults(newVideos);
          setRes({
            status: 200,
            data: { message: 'YouTube videos fetched successfully!' }
          });
        }
      })
      .catch((err) => {
        console.error('Youtube error:', err);
        setRes({
          status: err.status || 500,
          response: { data: { message: err.data?.detail || 'Failed to fetch YouTube videos.' } }
        });
      });

    Promise.allSettled([doubtSolverPromise, youtubeSearchPromise]).then(() => {
      setMorphState('celebrating');
      setText('');
      setAudio(null);
      setImage(null);
      setReset(false);
      setTimeout(() => {
        setMorphState('idle');
        setEnergyLevel(0);
      }, 3000);
    });
  };

  const handleHistoryClick = (historyItem) => {
    setResponse((prev) => [
      ...prev,
      { type: 'user', content: historyItem.question },
      { type: 'bot', content: historyItem.response }
    ]);
    setAudioUrl('');
    setFeedbackMessage('');
    setShowHistory(false);
  };

  const handleResetHistory = async () => {
    setHasUserInteracted(true);
    setReset(true);
    setResponseHistory([]);
    setResponse([]);
    setYoutubeResults([]);
    setPreviousYoutubeResults([]);
    setFeedbackMessage('History cleared.');
    setRes({
      status: 200,
      data: { message: 'History cleared successfully!' }
    });
    try {
      const formData = new FormData();
      formData.append('user_id', userId);
      formData.append('reset', 'true');
      formData.append('include_history', 'true');
      formData.append('mode', 'tutor');
      formData.append('language', 'english');
      const result = await doubtSolver(formData).unwrap();
      if (result.history) {
        const serverHistory = [];
        for (let i = 0; i < result.history.length; i++) {
          if (result.history[i].role === 'user') {
            const nextMessage = result.history[i + 1];
            serverHistory.push({
              question: result.history[i].content || 'Query',
              response:
                nextMessage && nextMessage.role === 'assistant' ? nextMessage.content || '' : ''
            });
          }
        }
        setResponseHistory(serverHistory);
      }
    } catch (err) {
      console.error('Reset history error:', err);
      setRes({
        status: err.status || 500,
        response: { data: { message: err.data?.detail || 'Failed to reset history.' } }
      });
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const getYouTubeVideoId = (url) => {
    const urlParams = new URLSearchParams(new URL(url).search);
    return urlParams.get('v') || url.split('v=')[1]?.split('&')[0] || url.split('/').pop();
  };

  const parseMarkdownToJSX = (markdown) => {
    if (!markdown) return [];

    const lines = markdown.split('\n').map((line) => line.trimEnd());
    const elements = [];
    let inList = false;

    lines.forEach((line, i) => {
      let trimmedLine = line.trim();

      if (trimmedLine.startsWith('## ')) {
        if (inList) {
          elements.push({ type: 'ul_end' });
          inList = false;
        }
        elements.push({ type: 'h2', content: trimmedLine.substring(3) });
      } else if (trimmedLine.startsWith('### ') || trimmedLine.startsWith('**Step')) {
        if (inList) {
          elements.push({ type: 'ul_end' });
          inList = false;
        }
        if (trimmedLine.startsWith('### ')) {
          elements.push({ type: 'h3', content: trimmedLine.substring(4) });
        } else {
          const boldedHeading = trimmedLine.replace(/\*\*(.*?)\*\*/g, '$1');
          elements.push({ type: 'h3', content: boldedHeading });
        }
      } else if (trimmedLine.startsWith('- ')) {
        if (!inList) {
          elements.push({ type: 'ul_start' });
          inList = true;
        }
        let listItemContent = line.substring(line.indexOf('- ') + 2).trimStart();
        elements.push({ type: 'li', content: listItemContent });
        if (i + 1 >= lines.length || !lines[i + 1].trimStart().startsWith('- ')) {
          elements.push({ type: 'ul_end' });
          inList = false;
        }
      } else if (trimmedLine === '---') {
        if (inList) {
          elements.push({ type: 'ul_end' });
          inList = false;
        }
        elements.push({ type: 'hr' });
      } else if (trimmedLine.startsWith('Answer:')) {
        if (inList) {
          elements.push({ type: 'ul_end' });
          inList = false;
        }
        let answerContent = trimmedLine.replace('Answer:', '').trim();
        answerContent = answerContent.replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold from answer
        elements.push({ type: 'answer', content: answerContent });
      } else if (line.trim().length > 0) {
        // Check for non-empty lines
        if (inList) {
          elements.push({ type: 'ul_end' });
          inList = false;
        }
        elements.push({ type: 'p', content: line });
      }
    });

    if (inList) {
      elements.push({ type: 'ul_end' });
    }

    return elements;
  };

  const renderMessageContent = (content) => {
    // This helper function handles mixed content with bolding and MathJax.
    return content.split(/(\$\$.*?\$\$|\$.*?\$|\*\*[^*]+\*\*)/g).map((segment, i) => {
      if (segment.startsWith('$$') && segment.endsWith('$$')) {
        return (
          <MathJax key={i} dynamic>
            {segment}
          </MathJax>
        );
      } else if (segment.startsWith('$') && segment.endsWith('$')) {
        return (
          <MathJax key={i} dynamic inline>
            {segment}
          </MathJax>
        );
      } else if (segment.startsWith('**') && segment.endsWith('**')) {
        return (
          <strong key={i} className="text-[#d9534f]">
            {segment.substring(2, segment.length - 2)}
          </strong>
        );
      } else {
        return segment;
      }
    });
  };

  const renderMessageText = (text) => {
    if (!text) return null;
    const parts = parseMarkdownToJSX(text);

    let ulContent = null;

    return (
      <div className="container">
        {parts.map((part, index) => {
          switch (part.type) {
            case 'h2':
              return (
                <h2 key={index} className="text-blue-700 text-2xl font-bold mt-8 mb-4">
                  {renderMessageContent(part.content)}
                </h2>
              );
            case 'h3':
              return (
                <h3
                  key={index}
                  className="text-gray-700 text-xl font-semibold mt-4 mb-2 border-b-2 border-gray-200 pb-2">
                  {renderMessageContent(part.content)}
                </h3>
              );
            case 'ul_start':
              ulContent = <ul key={index} className="list-disc list-inside space-y-2 mt-2" />;
              return ulContent;
            case 'ul_end':
              ulContent = null;
              return null;
            case 'li':
              return (
                <li key={index} className="text-gray-700">
                  {renderMessageContent(part.content)}
                </li>
              );
            case 'hr':
              return (
                <hr
                  key={index}
                  className="my-10 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent border-0"
                />
              );
            case 'answer':
              return (
                <div
                  key={index}
                  className="bg-green-50 rounded-lg p-4 border-l-4 border-green-500 my-4">
                  <p className="font-bold text-green-700">
                    Answer: {renderMessageContent(part.content)}
                  </p>
                </div>
              );
            case 'p':
              return (
                <p key={index} className="text-gray-700 mb-2">
                  {renderMessageContent(part.content)}
                </p>
              );
            default:
              return null;
          }
        })}
      </div>
    );
  };

  const formatMessage = (message, index) => {
    if (message.type === 'user') {
      return (
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex justify-end mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-3xl rounded-br-lg px-6 py-4 max-w-xs shadow-xl">
            <p className="text-sm font-medium">{message.content}</p>
          </div>
        </motion.div>
      );
    }
    return (
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex justify-start mb-6">
        <div className="flex items-start space-x-4">
          <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 shadow-xl max-w-[900px] overflow-hidden">
            {renderMessageText(message.content)}
            {index === response.length - 1 && (
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleGenerateAudio(message.content)}
                disabled={isAudioLoading}
                className="mt-3 px-4 py-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                {isAudioLoading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 1,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: 'linear'
                    }}
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  />
                ) : (
                  <>
                    <FaVolumeUp className="text-sm" />
                    <span>Generate Audio</span>
                  </>
                )}
              </motion.button>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  const handleCropComplete = async () => {
    if (imageRef.current && crop.width && crop.height) {
      try {
        const croppedImageBlob = await getCroppedImg(imageRef.current, crop, 'cropped-image.jpeg');
        const croppedImageFile = new File([croppedImageBlob], `cropped_${Date.now()}.jpeg`, {
          type: 'image/jpeg'
        });
        setImage(croppedImageFile);
        setCroppedImage(URL.createObjectURL(croppedImageBlob));
        setShowCropModal(false);
        setActiveInput('image');
        setMorphState('upload');
        setEnergyLevel(50);
        setRes({
          status: 200,
          data: { message: 'Image cropped and uploaded successfully!' }
        });
        setTimeout(() => setMorphState('idle'), 800);
      } catch (err) {
        console.error('Error cropping image:', err);
        setRes({
          status: 400,
          message: 'Failed to crop image. Please try again.'
        });
      }
    }
  };

  function getCroppedImg(image, crop, fileName) {
    const canvas = document.createElement('canvas');
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width,
      crop.height
    );
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Canvas is empty'));
            return;
          }
          blob.name = fileName;
          resolve(blob);
        },
        'image/jpeg',
        0.9
      );
    });
  }
  return (
    <MathJaxContext config={mathJaxConfig}>
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
        <Toastify res={res} resClear={() => setRes(null)} />
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full pointer-events-none"
            style={{
              left: particle.x,
              top: particle.y,
              width: particle.size,
              height: particle.size,
              backgroundColor: particle.color
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.2, 0.8, 0.2]
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.id * 0.3
            }}
          />
        ))}

        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="absolute top-0 left-0 right-0 z-20 p-4 flex items-center justify-end">
          <ReferTutorModal
            isOpen={showReferTutorModal}
            onClose={() => setShowReferTutorModal(false)}
            setFeedbackMessage={setFeedbackMessage}
          />

          <AnimatePresence>
            {showCropModal && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
                onClick={() => setShowCropModal(false)}>
                <motion.div
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0.9 }}
                  className="bg-white rounded-xl shadow-2xl p-6 max-w-3xl w-full max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-bold text-gray-800">Crop Your Image</h3>
                    <button
                      onClick={() => setShowCropModal(false)}
                      className="text-gray-500 hover:text-gray-700">
                      <FaTimes className="text-xl" />
                    </button>
                  </div>
                  <div className="mb-4">
                    <ReactCrop
                      crop={crop}
                      onChange={(newCrop) => setCrop(newCrop)}
                      onComplete={(c) => setCrop(c)}>
                      <img
                        ref={imageRef}
                        src={imageSrc || '/placeholder.svg'}
                        alt="Uploaded content"
                        className="max-h-[60vh]"
                      />
                    </ReactCrop>
                  </div>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => {
                        setShowCropModal(false);
                        setImageSrc(null);
                      }}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition">
                      Cancel
                    </button>
                    <button
                      onClick={handleCropComplete}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                      Apply Crop
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          <AnimatePresence>
            {showWebcamModal && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
                onClick={stopWebcam}>
                <motion.div
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0.9 }}
                  className="bg-white rounded-xl shadow-2xl p-6 max-w-3xl w-full max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-bold text-gray-800">Capture Live Image</h3>
                    <button onClick={stopWebcam} className="text-gray-500 hover:text-gray-700">
                      <FaTimes className="text-xl" />
                    </button>
                  </div>
                  <div className="mb-4">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      className="w-full max-h-[60vh] rounded-lg"
                    />
                  </div>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={stopWebcam}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition">
                      Cancel
                    </button>
                    <button
                      onClick={handleCaptureImage}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                      Capture
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowReferTutorModal(true)}
              className="cursor-pointer px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
              <motion.div
                transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}>
                <FaChalkboardTeacher className="text-sm" />
              </motion.div>
              <span>Refer a Tutor!</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowHistory(!showHistory)}
              className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white shadow-xl hover:shadow-2xl transition-all duration-300 relative">
              <FaHistory className="text-lg" />
              {responseHistory.length > 0 && (
                <motion.span
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-xs text-white flex items-center justify-center font-bold">
                  {responseHistory.length}
                </motion.span>
              )}
            </motion.button>
          </div>
        </motion.div>

        <div className="pt-20 pb-6 px-4 h-screen flex flex-col">
          <AnimatePresence>
            {!isYouTubeMaximized ? (
              <div className="flex w-full h-full">
                <div className="w-[60%] flex flex-col z-10">
                  <div
                    ref={chatContainerRef}
                    className="flex-1 overflow-y-auto overflow-x-hidden px-4 py-6 space-y-4"
                    style={{ maxHeight: 'calc(100vh - 120px)' }}>
                    {response.length === 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 50 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex flex-col items-center justify-center h-full text-center space-y-6">
                        <img
                          src={robotImg || '/placeholder.svg'}
                          alt="Robot"
                          className="w-46 h-36 object-contain"
                        />
                        <div className="space-y-3">
                          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            SPARKIT BOT
                          </h2>
                          <p className="text-gray-600 text-lg max-w-md">
                            Your intelligent learning companion is ready to help! Ask me anything
                            about your studies.
                          </p>
                        </div>
                      </motion.div>
                    )}
                    <AnimatePresence mode="popLayout">
                      {response.map((message, index) => (
                        <div key={index}>
                          {formatMessage(message, index)}
                          {message.type === 'bot' && index === response.length - 1 && audioUrl && (
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="flex justify-start ml-16 space-x-3 mb-6">
                              <motion.button
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                  if (audioRef.current) {
                                    if (isAudioPlaying) {
                                      audioRef.current.pause();
                                      setIsAudioPlaying(false);
                                    } else {
                                      audioRef.current.currentTime = 0;
                                      audioRef.current
                                        .play()
                                        .then(() => {
                                          console.log('Audio playback restarted');
                                          setIsAudioPlaying(true);
                                        })
                                        .catch((err) => {
                                          console.error('Audio playback failed:', err);
                                          setRes('Audio playback failed. Please try again.');
                                        });
                                    }
                                  }
                                }}
                                className="px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                                {isAudioPlaying ? (
                                  <FaStop className="text-sm" />
                                ) : (
                                  <FaPlay className="text-sm" />
                                )}
                                <span>{isAudioPlaying ? 'Stop Audio' : 'Play Audio'}</span>
                              </motion.button>
                            </motion.div>
                          )}
                        </div>
                      ))}
                    </AnimatePresence>
                    {aiThinking && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="flex justify-start mb-6">
                        <div className="flex items-start space-x-4">
                          <motion.div
                            animate={{
                              scale: [1, 1.2, 1],
                              rotate: [0, 360]
                            }}
                            transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                            className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                            <GiFairyWand className="text-white text-lg" />
                          </motion.div>
                          <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 shadow-xl">
                            <div className="flex items-center space-x-3">
                              <span className="text-gray-600 font-medium">SPARKIT is thinking</span>
                              <div className="flex space-x-1">
                                {[0, 1, 2].map((i) => (
                                  <motion.div
                                    key={i}
                                    animate={{
                                      scale: [1, 1.5, 1],
                                      opacity: [0.3, 1, 0.3]
                                    }}
                                    transition={{
                                      duration: 1.5,
                                      repeat: Number.POSITIVE_INFINITY,
                                      delay: i * 0.2
                                    }}
                                    className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full"
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                  <AnimatePresence>
                    {(audio || image) && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="mx-4 mb-4 flex flex-wrap gap-3">
                        {audio && (
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            className="flex items-center bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl px-4 py-3 shadow-lg">
                            <FaMicrophone className="text-blue-600 mr-3 text-lg" />
                            <span className="text-blue-800 font-medium mr-3">
                              {audio.name} {isRecording && `(${formatTime(recordingTime)})`}
                            </span>
                            <motion.button
                              whileHover={{ scale: 1.1, rotate: 5 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={handleDeleteAudio}
                              className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 transition-all duration-200">
                              <FaTrash className="text-sm" />
                            </motion.button>
                          </motion.div>
                        )}
                        {image && (
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            className="flex items-center bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl px-4 py-3 shadow-lg">
                            <div className="relative">
                              <img
                                src={croppedImage || URL.createObjectURL(image)}
                                alt="Uploaded content"
                                className="w-16 h-16 object-cover rounded-lg mr-3"
                              />
                              <motion.button
                                whileHover={{ scale: 1.1, rotate: 5 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={handleDeleteImage}
                                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-white flex items-center justify-center">
                                <FaTrash className="text-xs" />
                              </motion.button>
                            </div>
                            <span className="text-green-800 font-medium mr-3">
                              {image.name.length > 15
                                ? `${image.name.substring(0, 12)}...`
                                : image.name}
                            </span>
                          </motion.div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                  <motion.div
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    className="mx-4 bg-white rounded-3xl shadow-2xl border border-gray-100 p-4">
                    <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-100">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-600">Mode:</span>
                          <select
                            className="text-sm bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-xl px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                            value={mode}
                            onChange={(e) => setMode(e.target.value)}
                            disabled={isDoubtSolverLoading || isYoutubeLoading}>
                            <option value="text">Text</option>
                            <option value="tutor">Tutor</option>
                            <option value="conversation">Conversation</option>
                          </select>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-600">Language:</span>
                          <select
                            className="text-sm bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-xl px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                            value={language}
                            onChange={(e) => setLanguage(e.target.value)}
                            disabled={isDoubtSolverLoading || isYoutubeLoading}>
                            {Object.keys(languageMap).map((lang) => (
                              <option key={lang} value={lang}>
                                {lang}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-end space-x-3">
                      <div className="flex-1 relative">
                        <textarea
                          className="w-full p-4 pr-14 bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-white transition-all duration-300 resize-none"
                          placeholder="Ask me anything about your studies..."
                          value={text}
                          onChange={(e) => setText(e.target.value)}
                          disabled={isDoubtSolverLoading || isYoutubeLoading}
                          rows={2}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSubmit();
                            }
                          }}
                        />
                        <motion.button
                          whileHover={{ scale: 1.1, y: -2 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={handleSubmit}
                          disabled={
                            isDoubtSolverLoading || isYoutubeLoading || (!text && !audio && !image)
                          }
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                          title="Send Message">
                          {isDoubtSolverLoading || isYoutubeLoading ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{
                                duration: 1,
                                repeat: Number.POSITIVE_INFINITY,
                                ease: 'linear'
                              }}
                              className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                            />
                          ) : (
                            <FaPaperPlane className="text-lg" />
                          )}
                        </motion.button>
                      </div>
                      <div className="relative">
                        <motion.button
                          initial={{ scale: 1, opacity: 1 }}
                          animate={{ scale: [1, 1.1, 1], opacity: [1, 0.9, 1] }}
                          transition={{
                            duration: 1.5,
                            repeat: Number.POSITIVE_INFINITY,
                            repeatType: 'reverse'
                          }}
                          whileHover={{ scale: 1.2, y: -3 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => setShowMediaOptions(!showMediaOptions)}
                          className="w-12 h-12 bg-gradient-to-r from-gray-400 to-blue-500 rounded-2xl text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
                          title="Add Media">
                          <FaPlus className="text-lg" />
                        </motion.button>
                        <AnimatePresence>
                          {showMediaOptions && (
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: 20 }}
                              className="absolute bottom-full mb-4 mt-8 right-0 bg-white rounded-2xl shadow-lg p-2 flex flex-col space-y-2 z-10">
                              <motion.button
                                custom={0}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 20 }}
                                transition={{ delay: 0 }}
                                whileHover={{ scale: 1.1, y: -2 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => imageInputRef.current.click()}
                                disabled={isDoubtSolverLoading || isYoutubeLoading}
                                className="w-10 h-10 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                                title="Upload Image">
                                <FaImage className="text-lg" />
                              </motion.button>
                              <motion.button
                                custom={1}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 20 }}
                                transition={{ delay: 0.2 }}
                                whileHover={{ scale: 1.1, y: -2 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={startWebcam}
                                disabled={isDoubtSolverLoading || isYoutubeLoading}
                                className="w-10 h-10 bg-gradient-to-r from-teal-400 to-cyan-500 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                                title="Capture Live Image">
                                <FaVideo className="text-lg" />
                              </motion.button>
                              <motion.button
                                custom={1}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 20 }}
                                transition={{ delay: 0.2 }}
                                whileHover={{ scale: 1.1, y: -2 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => audioInputRef.current.click()}
                                disabled={isRecording || isDoubtSolverLoading || isYoutubeLoading}
                                className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                                title="Upload Audio">
                                <FaUpload className="text-lg" />
                              </motion.button>
                              <motion.button
                                custom={2}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 20 }}
                                transition={{ delay: 0.4 }}
                                whileHover={{ scale: 1.1, y: -2 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={handleRecord}
                                disabled={isDoubtSolverLoading || isYoutubeLoading}
                                className={`w-10 h-10 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center ${
                                  isRecording
                                    ? 'bg-gradient-to-r from-red-500 to-pink-600 animate-pulse'
                                    : 'bg-gradient-to-r from-purple-500 to-indigo-600'
                                }`}
                                title={isRecording ? 'Stop Recording' : 'Record Audio'}>
                                {isRecording ? (
                                  <FaStop className="text-lg" />
                                ) : (
                                  <FaMicrophone className="text-lg" />
                                )}
                              </motion.button>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </div>
                    <AnimatePresence>
                      {isRecording && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="flex items-center justify-center mt-3 space-x-3">
                          <motion.div
                            animate={{ scale: [1, 1.3, 1] }}
                            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
                            className="w-3 h-3 bg-red-500 rounded-full"
                          />
                          <span className="text-red-600 font-medium">
                            Recording... {formatTime(recordingTime)}
                          </span>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                  <AnimatePresence>
                    {feedbackMessage && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="mx-4 mt-3 p-4 bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl text-center shadow-lg">
                        <p className="text-green-800 font-medium">{feedbackMessage}</p>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  className="w-[40%] bg-white border-l border-gray-200 flex flex-col z-10">
                  <div className="flex items-center justify-between p-4 border-b">
                    <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                      Related Videos
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsYouTubeMaximized(true)}
                      className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration">
                      <FaExpand className="text-sm" />
                    </motion.button>
                  </div>
                  <div className="flex-1 overflow-y-auto p-4">
                    <YouTubeVideos
                      youtubeResults={
                        youtubeResults.length > 0 ? youtubeResults : previousYoutubeResults
                      }
                      isYoutubeLoading={isYoutubeLoading}
                      getYouTubeVideoId={getYouTubeVideoId}
                    />
                  </div>
                </motion.div>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="fixed inset-0 z-50 bg-white flex flex-col">
                <div className="flex items-center justify-between p-4 border-b">
                  <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                    Related Videos
                  </h3>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsYouTubeMaximized(false)}
                    className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-gradient-to-r from-red-100 to-pink-100 hover:text-red-600">
                    <FaArrowLeft className="text-lg" />
                  </motion.button>
                </div>
                <div className="flex-1 overflow-y-auto p-4">
                  <YouTubeVideos
                    youtubeResults={
                      youtubeResults.length > 0 ? youtubeResults : previousYoutubeResults
                    }
                    isYoutubeLoading={isYoutubeLoading}
                    getYouTubeVideoId={getYouTubeVideoId}
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <AnimatePresence>
          {showHistory && (
            <motion.div
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 300 }}
              className="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl border-l border-gray-200 z-50 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Question History
                </h3>
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setShowHistory(false)}
                  className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-gradient-to-r from-purple-100 to-blue-100 hover:text-purple-600">
                  <FaTimes className="text-lg" />
                </motion.button>
              </div>
              <div className="flex-1 overflow-y-auto mb-4">
                {responseHistory.length === 0 ? (
                  <div className="text-center py-12">
                    <motion.div
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                      className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FaHistory className="text-3xl text-purple-400" />
                    </motion.div>
                    <p className="text-gray-500 font-medium">No questions asked yet.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {responseHistory.map((item, index) => (
                      <motion.button
                        key={index}
                        whileHover={{ scale: 1.02, x: 5 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleHistoryClick(item)}
                        className="w-full text-left p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl hover:from-purple-100 hover:to-blue-100 border border-purple-100">
                        <p className="text-sm text-gray-800 font-medium line-clamp-3">
                          {item.question.length > 80
                            ? `${item.question.slice(0, 80)}...`
                            : item.question}
                        </p>
                      </motion.button>
                    ))}
                  </div>
                )}
              </div>
              <motion.button
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleResetHistory}
                disabled={isDoubtSolverLoading || isYoutubeLoading}
                className="w-full p-4 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl text-white font-bold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-3 disabled:opacity-50">
                <FaTrash className="text-lg" />
                <span>Clear History</span>
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {showVideoCall && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <motion.div
                initial={{ y: 50 }}
                animate={{ y: 0 }}
                className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Video Call
                  </h3>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setShowVideoCall(false)}
                    className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600">
                    <FaTimes className="text-lg" />
                  </motion.button>
                </div>
                <div className="flex items-center justify-center py-8">
                  <VideoCallButton />
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        <input
          type="file"
          accept="audio/*"
          ref={audioInputRef}
          onChange={handleAudioUpload}
          className="hidden"
        />
        <input
          type="file"
          accept="image/*"
          ref={imageInputRef}
          onChange={handleImageUpload}
          className="hidden"
        />

        <style jsx>{`
          .latex-content-wrapper {
            display: block;
            max-width: 100%;
            overflow: hidden;
            line-height: 1.6;
            word-wrap: break-word;
            overflow-wrap: break-word;
          }
          .latex-content-wrapper .katex-display {
            margin: 0.5em 0;
            overflow-x: auto;
            overflow-y: hidden;
            max-width: 100%;
            padding: 2px 0;
          }
          .latex-content-wrapper .katex {
            font-size: 0.95em;
            line-height: 1.4;
            max-width: 100%;
            overflow-wrap: break-word;
          }
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          ::-webkit-scrollbar {
            width: 6px;
          }
          ::-webkit-scrollbar-track {
            background: transparent;
          }
          ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #8b5cf6, #3b82f6);
            border-radius: 10px;
          }
          ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #7c3aed, #2563eb);
          }
        `}</style>
      </div>
    </MathJaxContext>
  );
};

export default ProblemSolver;
