import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import SideBar from './SideBar';
import TopBar from './TopBar';
import ChatBotSystem from '../../pages/screens/studentPanel/chatSupport/ChatBotSystem';
import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';

const Navbar = ({ children }) => {
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const userRole = sessionStorage.getItem('role');
  const location = useLocation();
  const navigate = useNavigate();

  const hiddenNavPaths = [
    '/sasthra/student/mock-test-simulation',
    '/sasthra/student/create-your-own-test',
    '/sasthra/student/ai-tutor',
    '/sasthra/teacher/live-streaming',
    '/sasthra/faculty/live-viewer',
    '/sasthra/student/problem-solver',
    '/sasthra/student/virtual-labs',
    '/sasthra/student/student-community',
    '/sasthra/faculty/ai-tutor'
  ];

  const isNavVisible = !hiddenNavPaths.includes(location.pathname);

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  const handleLinkClick = () => {
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {isNavVisible && <TopBar onToggleSidebar={toggleSidebar} />}

      <div className="flex flex-grow overflow-hidden relative">
        {isSidebarOpen && (
          <div
            onClick={toggleSidebar}
            className="fixed inset-0 bg-black/60 z-40 md:hidden" // Increased z-index
            aria-hidden="true"></div>
        )}

        {isNavVisible && <SideBar isOpen={isSidebarOpen} onLinkClick={handleLinkClick} />}

        {!isNavVisible && (
          <motion.div
            className="absolute top-4 left-4 z-50"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}>
            <motion.button
              onClick={() => navigate('/sasthra')}
              className="group flex items-center gap-2 px-4 py-2 bg-white text-gray-700 font-semibold rounded-lg shadow-sm hover:shadow-md border border-gray-200 transition-all duration-300 cursor-pointer"
              whileHover={{ scale: 1.05, backgroundColor: 'rgb(249 250 251)' }}
              whileTap={{ scale: 0.95 }}
              aria-label="Go back to the previous page">
              <ArrowLeft
                size={20}
                className="text-gray-500 transition-transform duration-300 group-hover:-translate-x-1"
              />
              Back
            </motion.button>
          </motion.div>
        )}

        <main className="flex-grow overflow-y-auto w-full">{children}</main>

        {userRole === 'student' && isNavVisible && (
          <div className="fixed bottom-4 right-4 z-20">
            <ChatBotSystem />
          </div>
        )}
      </div>
    </div>
  );
};

export default Navbar;
