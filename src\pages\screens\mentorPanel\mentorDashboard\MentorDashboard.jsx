import React from 'react';
import { useDirectorMentorDashboardServiceQuery } from './mentorDashboard.slice';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Loader2,
  Users,
  AlertCircle,
  User,
  BookOpen,
  Clock,
  BarChart2,
  MessageSquare,
  Award,
  Settings,
  LogOut,
  Sparkles,
  Rocket,
  BrainCircuit,
  GraduationCap,
  NotebookPen,
  CalendarCheck,
  FileText,
  Mic2,
  Video
} from 'lucide-react';

const MentorDashboard = () => {
  const { data: mentorData, isLoading, isError, error } = useDirectorMentorDashboardServiceQuery();
  const sessionnId = sessionStorage.getItem('activeSessionId');
  console.log(sessionnId);
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };
  const TypewriterEffect = ({ text, speed = 50, loop = false }) => {
    const [displayedText, setDisplayedText] = React.useState('');
    const [currentIndex, setCurrentIndex] = React.useState(0);

    React.useEffect(() => {
      if (currentIndex < text.length) {
        const timeout = setTimeout(() => {
          setDisplayedText((prev) => prev + text[currentIndex]);
          setCurrentIndex((prev) => prev + 1);
        }, speed);

        return () => clearTimeout(timeout);
      } else if (loop) {
        const timeout = setTimeout(() => {
          setDisplayedText('');
          setCurrentIndex(0);
        }, 2000);

        return () => clearTimeout(timeout);
      }
    }, [currentIndex, text, speed, loop]);

    return <span>{displayedText}</span>;
  };
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    },
    hover: {
      y: -5,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
    }
  };

  const statsVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        delay: 0.2
      }
    },
    hover: {
      scale: 1.05
    }
  };

  // Mock data for student progress
  const studentProgress = [
    { name: 'Alice Johnson', progress: 85, avatarColor: 'bg-pink-500' },
    { name: 'Bob Smith', progress: 72, avatarColor: 'bg-blue-500' },
    { name: 'Charlie Brown', progress: 93, avatarColor: 'bg-green-500' },
    { name: 'Diana Prince', progress: 68, avatarColor: 'bg-purple-500' }
  ];

  // Mock upcoming sessions
  const upcomingSessions = [
    { title: 'Advanced React Patterns', time: 'Today, 4:00 PM', status: 'upcoming' },
    { title: 'State Management Deep Dive', time: 'Wed, 4:00 PM', status: 'scheduled' },
    { title: 'Project Review Session', time: 'Fri, 4:00 PM', status: 'scheduled' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-50/30 p-4 md:p-8 overflow-hidden relative">
      {/* Floating Background Elements - More dynamic */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-indigo-100/20 rounded-full blur-3xl"
          animate={{
            x: [0, 20, 0],
            y: [0, 15, 0]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-blue-100/20 rounded-full blur-3xl"
          animate={{
            x: [0, -15, 0],
            y: [0, -10, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2
          }}
        />
        <motion.div
          className="absolute top-1/3 right-1/3 w-40 h-40 bg-purple-100/20 rounded-full blur-3xl"
          animate={{
            x: [0, 10, 0],
            y: [0, 20, 0]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 4
          }}
        />
      </div>

      {/* Floating particles */}
      {[...Array(20)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-indigo-200/30 pointer-events-none"
          style={{
            width: Math.random() * 10 + 5 + 'px',
            height: Math.random() * 10 + 5 + 'px',
            left: Math.random() * 100 + '%',
            top: Math.random() * 100 + '%'
          }}
          animate={{
            y: [0, (Math.random() - 0.5) * 100],
            x: [0, (Math.random() - 0.5) * 50],
            opacity: [0.3, 0.8, 0.3]
          }}
          transition={{
            duration: Math.random() * 20 + 10,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
        />
      ))}

      <motion.div
        className="max-w-7xl mx-auto relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible">
        {/* Header with animated gradient */}
        <motion.div className="flex justify-between items-center mb-8" variants={cardVariants}>
          <div>
            <motion.h1
              className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-black"
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%']
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: 'reverse',
                ease: 'linear'
              }}
              style={{
                backgroundSize: '200% 200%'
              }}>
              Welcome, Mentor
              <Sparkles className="inline ml-2 w-6 h-6 text-yellow-400" />
            </motion.h1>
            <motion.p
              className="text-gray-500 mt-1"
              animate={{
                x: [0, 2, 0]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: 'easeInOut'
              }}>
              Your personalized teaching dashboard
            </motion.p>
          </div>
        </motion.div>

        {/* Loading State - More engaging */}
        {isLoading && (
          <motion.div
            className="flex flex-col items-center justify-center h-[70vh]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}>
            {/* Animated mentor avatar construction */}
            <motion.div className="relative mb-8" style={{ width: 120, height: 120 }}>
              {/* Face outline */}
              <motion.div
                className="absolute inset-0 border-4 border-[var(--color-mentor)] rounded-full"
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6 }}
              />

              {/* Eyes */}
              <motion.div
                className="absolute top-8 left-6 w-6 h-6 bg-[var(--color-mentor)] rounded-full"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.4 }}
              />
              <motion.div
                className="absolute top-8 right-6 w-6 h-6 bg-[var(--color-mentor)] rounded-full"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.4 }}
              />

              {/* Smile */}
              <motion.path
                d="M30 70 Q60 90 90 70"
                stroke="currentColor"
                strokeWidth="4"
                fill="transparent"
                className="bg-[var(--color-mentor)] absolute top-14 left-15"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              />

              {/* Graduation cap */}
              <motion.div
                className="absolute -top-6 left-0 right-0 mx-auto w-16 h-8 bg-[var(--color-mentor)] rounded-t-lg"
                initial={{ y: -30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.4 }}
              />
              <motion.div
                className="absolute -top-2 left-0 right-0 mx-auto w-24 h-2 bg-[var(--color-mentor)]"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.9, duration: 0.3 }}
              />
            </motion.div>

            {/* Animated progress bar with floating elements */}
            <div className="w-full max-w-md px-4">
              <motion.div
                className="h-3 bg-gray-200 rounded-full overflow-hidden mb-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}>
                <motion.div
                  className="h-full bg-[var(--color-mentor)]"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: 'reverse',
                    ease: 'easeInOut'
                  }}
                />
              </motion.div>

              {/* Floating course elements */}
              <div className="flex justify-between relative h-8">
                {['Profile', 'Courses', 'Students', 'Analytics'].map((item, index) => (
                  <motion.div
                    key={item}
                    className="absolute"
                    style={{
                      left: `${25 * index}%`,
                      bottom: 0
                    }}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{
                      y: [0, -15, 0],
                      opacity: 1
                    }}
                    transition={{
                      delay: 0.5 + index * 0.2,
                      duration: 1.5,
                      repeat: Infinity,
                      repeatDelay: 1
                    }}>
                    <div className="flex flex-col items-center">
                      <div className="w-2 h-2 bg-[var(--color-mentor)] rounded-full mb-1" />
                      <span className="text-xs text-gray-500 whitespace-nowrap">{item}</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Animated text with typing effect */}
            <motion.div className="mt-12 text-center">
              <motion.h3
                className="text-xl font-semibold text-gray-700 mb-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}>
                Building Your Mentor Space
              </motion.h3>
              <motion.p
                className="text-gray-500 max-w-md"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}>
                <TypewriterEffect
                  text="Loading your courses, students, and analytics..."
                  speed={50}
                  loop={true}
                />
              </motion.p>
            </motion.div>

            {/* Floating inspirational quotes */}
            <motion.div
              className="mt-8 text-sm text-gray-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}>
              <motion.div
                animate={{
                  opacity: [0.6, 1, 0.6],
                  y: [0, -3, 0]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity
                }}>
                "Great mentors don't just teach - they inspire"
              </motion.div>
            </motion.div>
          </motion.div>
        )}

        {/* Error State - More visual */}
        {isError && (
          <motion.div
            className="bg-gradient-to-r from-red-100/80 to-red-50/50 backdrop-blur-md p-8 rounded-2xl shadow-lg flex flex-col items-center justify-center text-center border border-red-200/50"
            variants={statsVariants}
            initial="hidden"
            animate="visible">
            <motion.div
              className="bg-red-100 p-4 rounded-full mb-4 relative"
              animate={{
                scale: [1, 1.05, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: 'mirror'
              }}>
              <AlertCircle className="h-12 w-12 text-red-600" />
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-red-300 opacity-0"
                animate={{
                  scale: [1, 1.8],
                  opacity: [0.8, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity
                }}
              />
            </motion.div>
            <h3 className="text-2xl font-bold text-red-800 mb-3">Connection Error</h3>
            <p className="text-red-600 max-w-md text-lg mb-6">
              {error?.data?.error ||
                'Failed to connect to the mentor network. Please check your connection.'}
            </p>
            <motion.button
              whileHover={{
                scale: 1.02,
                boxShadow: '0 5px 15px -5px rgba(239, 68, 68, 0.4)'
              }}
              whileTap={{ scale: 0.98 }}
              className="px-8 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full shadow-md font-medium flex items-center gap-2">
              <Rocket className="w-5 h-5" />
              Retry Connection
            </motion.button>
          </motion.div>
        )}

        {/* Success State */}
        <AnimatePresence>
          {mentorData && !isLoading && !isError && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Profile Card - Enhanced */}
              <motion.div
                className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 relative"
                variants={cardVariants}
                whileHover="hover">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-blue-600/5 -z-10" />
                <div className="bg-[var(--color-mentor)] p-6 text-white relative overflow-hidden">
                  <div className="absolute -right-10 -top-10 w-32 h-32 bg-white/10 rounded-full" />
                  <div className="absolute -right-5 -bottom-5 w-20 h-20 bg-white/5 rounded-full" />

                  <div className="flex items-center gap-4 relative z-10">
                    <div className="relative">
                      <motion.div
                        className="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center text-3xl font-bold text-white shadow-lg"
                        whileHover={{ scale: 1.05 }}>
                        {mentorData.mendor.first_name?.charAt(0)}
                        {mentorData.mendor.last_name?.charAt(0)}
                      </motion.div>
                      <motion.div
                        className="absolute bottom-0 right-0 w-6 h-6 bg-green-400 rounded-full border-2 border-white"
                        animate={{
                          scale: [1, 1.2, 1],
                          boxShadow: [
                            '0 0 0 0 rgba(74, 222, 128, 0.7)',
                            '0 0 0 10px rgba(74, 222, 128, 0)'
                          ]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity
                        }}
                      />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">
                        {mentorData.mendor.first_name} {mentorData.mendor.last_name}
                      </h2>
                      <p className="text-indigo-100/90 flex items-center gap-1 mt-1">
                        <GraduationCap className="w-4 h-4" />
                        Senior Mentor
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    <motion.div
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
                      whileHover={{ x: 5 }}>
                      <div className="p-2 bg-indigo-100/50 rounded-lg">
                        <User className="w-5 h-5 text-indigo-500" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Role</p>
                        <p className="font-medium">Course Mentor</p>
                      </div>
                    </motion.div>

                    <motion.div
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
                      whileHover={{ x: 5 }}>
                      <div className="p-2 bg-blue-100/50 rounded-lg">
                        <MessageSquare className="w-5 h-5 text-blue-500" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="font-medium">{mentorData.mendor.email}</p>
                      </div>
                    </motion.div>

                    <motion.div
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
                      whileHover={{ x: 5 }}>
                      <div className="p-2 bg-purple-100/50 rounded-lg">
                        <BookOpen className="w-5 h-5 text-purple-500" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Specialization</p>
                        <p className="font-medium">{mentorData.mendor.subject_name}</p>
                      </div>
                    </motion.div>
                  </div>

                  <motion.div
                    className="mt-6 pt-4 border-t border-gray-200/50 flex justify-between"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}></motion.div>
                </div>
              </motion.div>

              {/* Main Content Area */}
              <div className="lg:col-span-2 space-y-6">
                {/* Stats Grid - Enhanced */}
                <motion.div
                  className="grid grid-cols-2 md:grid-cols-4 gap-4"
                  variants={statsVariants}>
                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-indigo-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Students</p>
                        <p className="text-2xl font-bold text-indigo-600">42</p>
                        <motion.p
                          className="text-xs text-green-500 mt-1 flex items-center"
                          animate={{ opacity: [0.6, 1, 0.6] }}
                          transition={{ duration: 2, repeat: Infinity }}>
                          +3 this week
                        </motion.p>
                      </div>
                      <div className="p-2 bg-indigo-100/50 rounded-lg">
                        <Users className="w-6 h-6 text-indigo-600" />
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-blue-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Courses</p>
                        <p className="text-2xl font-bold text-blue-600">3</p>
                      </div>
                      <div className="p-2 bg-blue-100/50 rounded-lg">
                        <BookOpen className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-purple-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Sessions</p>
                        <p className="text-2xl font-bold text-purple-600">18</p>
                        <p className="text-xs text-gray-400 mt-1">4 completed</p>
                      </div>
                      <div className="p-2 bg-purple-100/50 rounded-lg">
                        <Clock className="w-6 h-6 text-purple-600" />
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-green-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Rating</p>
                        <p className="text-2xl font-bold text-green-600">4.8</p>
                        <div className="flex mt-1">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon key={i} filled={i < 4} half={i === 4} />
                          ))}
                        </div>
                      </div>
                      <div className="p-2 bg-green-100/50 rounded-lg">
                        <Award className="w-6 h-6 text-green-600" />
                      </div>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Course Details Card - Enhanced */}
                <motion.div
                  className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 relative"
                  variants={cardVariants}
                  whileHover="hover">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30 -z-10" />
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-6">
                      <h2 className="text-xl font-bold text-[var(--color-mentor)] flex items-center gap-2">
                        <NotebookPen className="text-indigo-500" />
                        Course Information
                      </h2>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                        className="px-3 py-1 bg-[var(--color-mentor)] text-white rounded-full text-xs font-medium">
                        View All
                      </motion.button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Current Course</p>
                          <p className="font-medium text-lg text-indigo-600">
                            {mentorData.mendor.course_name}
                          </p>
                        </motion.div>

                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Subject</p>
                          <p className="font-medium text-lg text-blue-600">
                            {mentorData.mendor.subject_name}
                          </p>
                        </motion.div>
                      </div>

                      <div className="space-y-4">
                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Batch Schedule</p>
                          <p className="font-medium">Mon, Wed, Fri • 4:00-6:00 PM</p>
                          <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              className="bg-indigo-500 h-1.5 rounded-full"
                              style={{ width: '70%' }}></div>
                          </div>
                        </motion.div>

                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Next Session</p>
                          <div className="flex items-center justify-between">
                            <p className="font-medium">Tomorrow • 4:00 PM</p>
                            <motion.div
                              whileHover={{ scale: 1.1 }}
                              className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center">
                              <CalendarCheck className="w-4 h-4 text-indigo-600" />
                            </motion.div>
                          </div>
                        </motion.div>
                      </div>
                    </div>

                    <div className="mt-6 pt-4 border-t border-gray-200/50">
                      <h3 className="text-sm font-medium text-gray-500 mb-3">Quick Actions</h3>
                      <div className="flex flex-wrap gap-3">
                        <motion.button
                          whileHover={{
                            y: -3,
                            boxShadow: '0 5px 15px -5px rgba(99, 102, 241, 0.4)'
                          }}
                          whileTap={{ scale: 0.98 }}
                          className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
                          <Mic2 className="w-4 h-4" />
                          Start Session
                        </motion.button>
                        <motion.button
                          whileHover={{
                            y: -3,
                            boxShadow: '0 5px 15px -5px rgba(59, 130, 246, 0.4)'
                          }}
                          whileTap={{ scale: 0.98 }}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Upload Materials
                        </motion.button>
                        <motion.button
                          whileHover={{
                            y: -3,
                            boxShadow: '0 5px 15px -5px rgba(139, 92, 246, 0.4)'
                          }}
                          whileTap={{ scale: 0.98 }}
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
                          <Video className="w-4 h-4" />
                          Record Lecture
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Additional Cards Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Student Progress Card */}
                  <motion.div
                    className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 p-6"
                    variants={cardVariants}
                    whileHover="hover">
                    <h2 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <BarChart2 className="text-green-500" />
                      Student Progress
                    </h2>
                    <div className="space-y-4">
                      {studentProgress.map((student, index) => (
                        <motion.div
                          key={index}
                          className="flex items-center gap-3"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}>
                          <div
                            className={`w-10 h-10 rounded-full ${student.avatarColor} flex items-center justify-center text-white font-medium`}>
                            {student.name.charAt(0)}
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-800">{student.name}</p>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full"
                                style={{ width: `${student.progress}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-sm font-medium text-gray-500">
                            {student.progress}%
                          </span>
                        </motion.div>
                      ))}
                    </div>
                    <motion.button
                      whileHover={{ y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      className="mt-4 w-full py-2 text-sm font-medium text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">
                      View All Students
                    </motion.button>
                  </motion.div>

                  {/* Upcoming Sessions Card */}
                  <motion.div
                    className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 p-6"
                    variants={cardVariants}
                    whileHover="hover">
                    <h2 className="text-lg font-bold text-black mb-4 flex items-center gap-2">
                      <Clock className="text-purple-500" />
                      Upcoming Sessions
                    </h2>
                    <div className="space-y-3">
                      {upcomingSessions.map((session, index) => (
                        <motion.div
                          key={index}
                          className={`p-3 rounded-lg border ${session.status === 'upcoming' ? 'bg-purple-50/50 border-purple-200' : 'bg-gray-50/50 border-gray-200'}`}
                          whileHover={{ scale: 1.02 }}>
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium text-gray-800">{session.title}</p>
                              <p className="text-sm text-gray-500 mt-1">{session.time}</p>
                            </div>
                            {session.status === 'upcoming' && (
                              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
                                Today
                              </span>
                            )}
                          </div>
                          {session.status === 'upcoming' && (
                            <motion.button
                              whileHover={{ scale: 1.03 }}
                              whileTap={{ scale: 0.98 }}
                              className="mt-2 w-full py-1.5 bg-purple-600 text-white text-sm rounded-lg font-medium">
                              Prepare Session
                            </motion.button>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

// Star rating component
const StarIcon = ({ filled, half }) => {
  return (
    <svg
      className={`w-3 h-3 ${filled ? 'text-yellow-400' : half ? 'text-yellow-400/50' : 'text-gray-300'}`}
      fill="currentColor"
      viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  );
};

export default MentorDashboard;
