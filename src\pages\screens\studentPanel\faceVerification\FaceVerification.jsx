import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCamera,
  faCheckCircle,
  faTimesCircle,
  faArrowLeft,
  faPowerOff,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { useVerifyFaceMutation } from './faceVerication.slice';
import Toastify from '../../../../components/PopUp/Toastify';

const COUNSELOR_COLOR = 'var(--color-counselor)'; // Fallback to '#f4c430' if undefined

const FaceVerification = ({
  showVerification,
  setShowVerification,
  userId,
  onVerificationSuccess
}) => {
  const [verifyFace, { isLoading, error, data }] = useVerifyFaceMutation();
  const [cameraStream, setCameraStream] = useState(null);
  const [capturedImage, setCapturedImage] = useState(null);
  const [res, setRes] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null); // Dedicated state for error handling
  const [shouldRedirect, setShouldRedirect] = useState(false); // New state for redirect trigger
  const videoRef = useRef(null);
  const prevResRef = useRef(null); // Track previous res to prevent duplicate updates
  const navigate = useNavigate();

  // Get userId from sessionStorage if not provided
  const effectiveUserId = userId || sessionStorage.getItem('userId');

  // Start camera when modal opens
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480, facingMode: 'user' }
      });
      setCameraStream(stream);
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      const message = `Error accessing camera: ${err.message}`;
      if (!prevResRef.current || prevResRef.current.message !== message) {
        setRes({ success: false, message });
        setErrorMessage(message);
        setShouldRedirect(true); // Trigger redirect on camera error
      }
      console.error('Camera error:', err);
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (cameraStream) {
      cameraStream.getTracks().forEach((track) => track.stop());
      setCameraStream(null);
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
  };

  // Capture image as Blob
  const captureImage = () => {
    if (!videoRef.current || !cameraStream) {
      const message = 'Camera not active. Please start the camera.';
      if (!prevResRef.current || prevResRef.current.message !== message) {
        setRes({ success: false, message });
        setErrorMessage(message);
        setShouldRedirect(true); // Trigger redirect on capture error
      }
      return;
    }

    const canvas = document.createElement('canvas');
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(videoRef.current, 0, 0);

    canvas.toBlob(
      (blob) => {
        const file = new File([blob], 'capture.jpg', { type: 'image/jpeg' });
        setCapturedImage(file);
      },
      'image/jpeg',
      0.8
    );
  };

  // Submit image for verification
  const submitVerification = async () => {
    if (!capturedImage) {
      const message = 'Please capture an image first.';
      if (!prevResRef.current || prevResRef.current.message !== message) {
        setRes({ success: false, message });
        setErrorMessage(message);
        setShouldRedirect(true); // Trigger redirect on no image error
      }
      return;
    }
    if (!effectiveUserId) {
      const message = 'No user ID found. Please log in.';
      if (!prevResRef.current || prevResRef.current.message !== message) {
        setRes({ success: false, message });
        setErrorMessage(message);
        setShouldRedirect(true); // Trigger redirect on no userId error
      }
      return;
    }

    try {
      const result = await verifyFace({
        userId: effectiveUserId,
        imageFile: capturedImage
      }).unwrap();
      setRes(result);
      stopCamera();
      if (onVerificationSuccess) {
        onVerificationSuccess({
          success: result.success,
          message:
            result.message ||
            (result.success ? 'Face verified successfully!' : 'Face does not match.'),
          similarityScore: result.similarityScore
        });
      }
      if (!result.success) {
        setErrorMessage(result.message || 'Face does not match.');
        setShouldRedirect(true); // Trigger redirect on verification failure
      }
    } catch (err) {
      const errorMessage = err.message || 'Error verifying face';
      setRes({ success: false, message: errorMessage, similarityScore: null });
      setErrorMessage(errorMessage);
      setShouldRedirect(true); // Trigger redirect on verification error
      if (onVerificationSuccess) {
        onVerificationSuccess({
          success: false,
          message: errorMessage,
          similarityScore: null
        });
      }
    }
  };

  // Handle close action
  const handleClose = () => {
    stopCamera(); // Ensure camera stops before navigation
    setShowVerification(false);
    setCapturedImage(null);
    navigate('/sasthra');
  };

  // Initialize camera when modal opens
  useEffect(() => {
    if (showVerification) {
      startCamera();
    } else {
      stopCamera(); // Stop camera when modal closes
    }
  }, [showVerification]);

  // Handle API response and error
  useEffect(() => {
    // Skip if data/error is unchanged to prevent infinite loop
    if (data === prevResRef.current?.data && error === prevResRef.current?.error) return;

    if (error) {
      const errorMessage = error.message || 'Error verifying face';
      if (!prevResRef.current || prevResRef.current.message !== errorMessage) {
        setRes({ success: false, message: errorMessage, similarityScore: null });
        setErrorMessage(errorMessage);
        setShouldRedirect(true); // Trigger redirect on API error
      }
      if (onVerificationSuccess) {
        onVerificationSuccess({
          success: false,
          message: errorMessage,
          similarityScore: null
        });
      }
    } else if (data) {
      const message =
        data.message || (data.success ? 'Face verified successfully!' : 'Face does not match.');
      if (!prevResRef.current || prevResRef.current.message !== message) {
        setRes({ success: data.success, message, similarityScore: data.similarityScore });
      }
      if (onVerificationSuccess) {
        onVerificationSuccess({
          success: data.success,
          message,
          similarityScore: data.similarityScore
        });
      }
    }

    // Update prevResRef with current data/error
    prevResRef.current = { data, error, message: res?.message };
  }, [data, error, onVerificationSuccess, res?.message]);

  // Handle redirect on error or success
  useEffect(() => {
    let timer;
    if (shouldRedirect) {
      timer = setTimeout(() => {
        stopCamera(); // Stop camera synchronously before navigation
        navigate('/sasthra');
        setShowVerification(false);
        setCapturedImage(null);
        setRes(null);
        setErrorMessage(null);
        setShouldRedirect(false); // Clear redirect flag after navigation
      }, 2000); // Redirect after 2 seconds on error
    } else if (res && res.success) {
      stopCamera(); // Stop camera immediately on success
      timer = setTimeout(() => {
        setShowVerification(false);
        setCapturedImage(null);
        setRes(null);
      }, 2000); // Auto-close after 2 seconds on success
    }
    return () => {
      clearTimeout(timer);
      stopCamera(); // Cleanup to stop camera on unmount
    };
  }, [shouldRedirect, res, navigate]);

  if (!showVerification) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ background: 'rgba(0,0,0,0.35)', backdropFilter: 'blur(8px)' }}>
      <Toastify res={res} resClear={() => setRes(null)} />
      {isLoading && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center z-60"
          style={{ background: 'rgba(0,0,0,0.5)', backdropFilter: 'blur(10px)' }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}>
          <motion.div
            className="relative w-24 h-24"
            animate={{ rotate: 360 }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'linear'
            }}>
            <div
              className="absolute inset-0 rounded-full border-4 border-t-yellow-400 border-b-transparent border-l-transparent border-r-transparent"
              style={{ background: 'rgba(255,255,255,0.2)', backdropFilter: 'blur(5px)' }}></div>
            <div
              className="absolute inset-2 rounded-full border-4 border-t-yellow-500 border-b-transparent border-l-transparent border-r-transparent"
              style={{ background: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(3px)' }}></div>
            <div className="absolute inset-4 rounded-full border-4 border-t-yellow-600 border-b-transparent border-l-transparent border-r-transparent"></div>
          </motion.div>
        </motion.div>
      )}
      <div
        className="relative max-w-md w-full mx-2 p-0 rounded-2xl shadow-2xl border-0"
        style={{ background: 'rgba(255,255,255,0.7)', backdropFilter: 'blur(16px)' }}>
        <button
          type="button"
          onClick={handleClose}
          className="absolute top-3 right-4 text-black hover:text-yellow-500 text-2xl font-extrabold z-10"
          style={{ background: 'none', border: 'none', boxShadow: 'none' }}>
          <FontAwesomeIcon icon={faTimes} />
        </button>
        <div className="flex flex-col items-center py-6 px-3">
          <div className="mb-4 flex flex-col items-center">
            <div className="relative">
              <div className="absolute -inset-2 bg-yellow-400 rounded-full blur opacity-30"></div>
              <div className="relative bg-white/60 rounded-full p-3 shadow-lg mb-1 border-2 border-yellow-400">
                <FontAwesomeIcon icon={faCamera} className="text-2xl text-black" />
              </div>
            </div>
            <h3 className="text-xl font-extrabold text-black mb-1 tracking-tight">
              Face Verification
            </h3>
            <p className="text-sm text-yellow-700 font-medium">Verify Your Identity</p>
          </div>

          {/* Video Preview */}
          <div className="relative mb-6">
            <div className="absolute -inset-1 bg-yellow-400 rounded-full blur opacity-30"></div>
            <div className="relative w-40 h-40 rounded-full overflow-hidden border-4 border-yellow-400 shadow-xl flex items-center justify-center bg-white/40">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                className="object-cover w-full h-full"
                style={{ borderRadius: '50%' }}
              />
              {capturedImage && (
                <div className="absolute inset-0 bg-yellow-400/30 flex items-center justify-center rounded-full">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-white text-2xl" />
                </div>
              )}
            </div>
          </div>

          {/* Capture and Control Buttons */}
          <div className="grid grid-cols-2 gap-3 mb-6 w-full px-4">
            <motion.button
              type="button"
              onClick={captureImage}
              className={`relative overflow-hidden w-full py-2.5 rounded-lg font-bold text-sm shadow-md border transition-all hover:cursor-pointer duration-200 ${
                capturedImage
                  ? 'bg-yellow-400 border-yellow-500 text-black'
                  : 'bg-white/80 border-yellow-300 text-yellow-700 hover:bg-yellow-100'
              }`}
              style={{ borderColor: COUNSELOR_COLOR }}
              disabled={!cameraStream || isLoading}
              whileHover={{ scale: 1.07, boxShadow: `0 2px 8px ${COUNSELOR_COLOR}` }}
              whileTap={{ scale: 0.95 }}>
              {capturedImage ? (
                <>
                  <div className="absolute inset-0 bg-white/20"></div>
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1.5" />
                  Captured
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faCamera} className="mr-1.5" />
                  Capture
                </>
              )}
            </motion.button>
            <motion.button
              type="button"
              onClick={submitVerification}
              className="relative overflow-hidden w-full py-2.5 rounded-lg font-bold text-sm shadow-md border bg-yellow-400 border-yellow-500 text-black hover:bg-yellow-500 transition-all"
              style={{ borderColor: COUNSELOR_COLOR }}
              disabled={!capturedImage || isLoading}
              whileHover={{ scale: 1.07, boxShadow: `0 2px 8px ${COUNSELOR_COLOR}` }}
              whileTap={{ scale: 0.95 }}>
              <div className="absolute inset-0 bg-white opacity-0 hover:opacity-20 transition-opacity"></div>
              {isLoading ? (
                <>
                  <FontAwesomeIcon icon={faTimesCircle} spin className="text-yellow-700" />
                  Verifying...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1.5" />
                  Verify
                </>
              )}
            </motion.button>
          </div>

          {/* Camera Controls */}
          <div className="flex flex-col md:flex-row justify-between items-center w-full gap-3 mb-2 px-4">
            <div className="flex gap-2 w-full md:w-auto">
              <motion.button
                type="button"
                onClick={startCamera}
                className="relative overflow-hidden flex-1 bg-white/80 border hover:cursor-pointer border-green-400 text-green-700 font-bold px-3 py-2 rounded-lg hover:bg-green-50 shadow-md transition-all text-sm"
                disabled={cameraStream || isLoading}
                whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #22c55e' }}
                whileTap={{ scale: 0.95 }}>
                <div className="absolute inset-0 bg-green-400 opacity-0 hover:opacity-10 transition-opacity"></div>
                <FontAwesomeIcon icon={faPowerOff} className="mr-2 text-green-600" />
                {cameraStream ? 'Camera Active' : 'Start Camera'}
              </motion.button>
              <motion.button
                type="button"
                onClick={stopCamera}
                className="relative overflow-hidden flex-1 bg-white/80 border hover:cursor-pointer border-red-400 text-red-700 font-bold px-3 py-2 rounded-lg hover:bg-red-50 shadow-md transition-all text-sm"
                disabled={!cameraStream || isLoading}
                whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #ef4444' }}
                whileTap={{ scale: 0.95 }}>
                <div className="absolute inset-0 bg-red-400 opacity-0 hover:opacity-10 transition-opacity"></div>
                <FontAwesomeIcon icon={faPowerOff} className="mr-2 text-red-600" />
                Stop
              </motion.button>
            </div>
            <motion.button
              type="button"
              onClick={handleClose}
              className="relative overflow-hidden w-full md:w-auto bg-gray-200 hover:cursor-pointer border border-gray-400 text-gray-700 font-bold px-3 py-2 rounded-lg hover:bg-gray-300 shadow-md transition-all text-sm"
              whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #888' }}
              whileTap={{ scale: 0.95 }}>
              <div className="absolute inset-0 bg-gray-400 opacity-0 hover:opacity-10 transition-opacity"></div>
              <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
              Cancel
            </motion.button>
          </div>

          {/* Verification Result */}
          {res && (
            <AnimatePresence>
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.7, opacity: 0 }}
                transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                className="mt-4 p-4 rounded-xl shadow-xl border-2"
                style={{
                  background: res.success
                    ? `linear-gradient(135deg, #fff 0%, ${COUNSELOR_COLOR} 100%)`
                    : 'linear-gradient(135deg, #fff 0%, #f87171 100%)',
                  borderColor: res.success ? COUNSELOR_COLOR : '#f87171'
                }}>
                <div className="flex items-center mb-2">
                  <FontAwesomeIcon
                    icon={res.success ? faCheckCircle : faTimesCircle}
                    className={
                      res.success ? 'text-yellow-500 text-2xl mr-2' : 'text-red-500 text-2xl mr-2'
                    }
                  />
                  <h4
                    className="text-lg font-bold"
                    style={{ color: res.success ? COUNSELOR_COLOR : '#b91c1c' }}>
                    {res.success ? 'Success!' : 'Failed'}
                  </h4>
                </div>
                <div className="text-black text-base">
                  <p>
                    <strong>Message:</strong> {res.message}
                  </p>
                  {res.similarityScore !== null && (
                    <p>
                      <strong>Similarity Score:</strong> {(res.similarityScore * 100).toFixed(2)}%
                    </p>
                  )}
                </div>
                {res.success && (
                  <motion.div
                    className="mt-2 text-yellow-700 text-sm font-semibold"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ delay: 0.2 }}>
                    This popup will close automatically.
                  </motion.div>
                )}
                {!res.success && (
                  <motion.div
                    className="mt-2 text-red-700 text-sm font-semibold"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ delay: 0.2 }}>
                    Redirecting to login in 2 seconds...
                  </motion.div>
                )}
              </motion.div>
            </AnimatePresence>
          )}
        </div>
      </div>
    </div>
  );
};

export default FaceVerification;
